# Full-Stack REST API Application

A complete full-stack application with a Go REST API backend and vanilla JavaScript frontend.

## Project Structure

```
project1/
├── backend/                 # Go REST API Backend
│   ├── main.go             # Main Go application
│   ├── go.mod              # Go module dependencies
│   ├── go.sum              # Go module checksums
│   ├── Dockerfile          # Docker container configuration
│   ├── docker-compose.yml  # Docker Compose setup
│   ├── run-docker.sh       # Docker management script
│   └── README.md           # Backend documentation
├── frontend/               # JavaScript Frontend
│   ├── index.html          # Main HTML page
│   ├── styles.css          # CSS styling
│   ├── script.js           # JavaScript functionality
│   ├── serve.py            # Python development server
│   └── README.md           # Frontend documentation
└── README.md               # This file
```

## Features

### Backend (Go + Docker)
- ✅ RESTful API with CRUD operations
- ✅ In-memory data storage
- ✅ Docker containerization
- ✅ Health check endpoint
- ✅ JSON request/response handling
- ✅ Proper HTTP status codes

### Frontend (HTML + CSS + JavaScript)
- ✅ Modern, responsive UI
- ✅ Real-time API interaction
- ✅ Create, read, update, delete items
- ✅ Health status monitoring
- ✅ Success/error notifications
- ✅ Modal dialogs for editing
- ✅ Mobile-friendly design

## Quick Start

### Option 1: Full-Stack Docker (Recommended)

```bash
# Build and start both frontend and backend
./docker-run.sh start

# Or using docker-compose directly
docker-compose up -d --build
```

### Option 2: Individual Docker Containers

#### Backend Only:
```bash
cd backend
docker build -t simple-rest-api-backend .
docker run -d -p 8080:8080 --name simple-rest-api-backend simple-rest-api-backend
```

#### Frontend Only:
```bash
cd frontend
docker build -t simple-rest-api-frontend .
docker run -d -p 3000:80 --name simple-rest-api-frontend simple-rest-api-frontend
```

### Option 3: Local Development

#### Backend:
```bash
cd backend
go run main.go
```

#### Frontend:
```bash
cd frontend
python3 serve.py
```

### 🌐 Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **Health Check**: http://localhost:8080/health

## API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check |
| GET | `/items` | Get all items |
| GET | `/items/{id}` | Get item by ID |
| POST | `/items` | Create new item |
| PUT | `/items/{id}` | Update item |
| DELETE | `/items/{id}` | Delete item |

## Usage Examples

### Using the Frontend
1. Open http://localhost:3000 in your browser
2. Click "Check Health" to verify API connection
3. Create new items using the form
4. View, edit, or delete items in the list
5. All changes are reflected in real-time

### Using the API Directly

```bash
# Health check
curl http://localhost:8080/health

# Get all items
curl http://localhost:8080/items

# Create new item
curl -X POST http://localhost:8080/items \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Item", "description": "A test item"}'

# Update item
curl -X PUT http://localhost:8080/items/1 \
  -H "Content-Type: application/json" \
  -d '{"name": "Updated Item", "description": "Updated description"}'

# Delete item
curl -X DELETE http://localhost:8080/items/1
```

## Development

### Backend Development
```bash
cd backend

# Install dependencies
go mod tidy

# Run locally (without Docker)
go run main.go

# Build Docker image
docker build -t simple-rest-api .

# Run tests (if you add them)
go test ./...
```

### Frontend Development
```bash
cd frontend

# Start development server
python3 serve.py

# The server includes CORS headers for API communication
# Edit files and refresh browser to see changes
```

## Docker Management

### Full-Stack Management (Recommended)

Use the main management script to control both frontend and backend:

```bash
# Start the full application
./docker-run.sh start

# Check status and health
./docker-run.sh status

# View logs (all services)
./docker-run.sh logs

# View specific service logs
./docker-run.sh logs backend
./docker-run.sh logs frontend

# Test API endpoints
./docker-run.sh test

# Stop the application
./docker-run.sh stop

# Restart the application
./docker-run.sh restart

# Clean up everything
./docker-run.sh clean

# Development mode (with logs)
./docker-run.sh dev
```

### Individual Service Management

#### Backend only:
```bash
cd backend
./run-docker.sh start    # Start container
./run-docker.sh stop     # Stop container
./run-docker.sh status   # Show status
./run-docker.sh logs     # View logs
./run-docker.sh test     # Test API endpoints
```

#### Frontend only:
```bash
cd frontend
docker build -t simple-rest-api-frontend .
docker run -d -p 3000:80 --name simple-rest-api-frontend simple-rest-api-frontend
```

## Customization

### Adding New API Endpoints
1. Edit `backend/main.go`
2. Add new handler functions
3. Register routes in the main function
4. Rebuild Docker container

### Adding Frontend Features
1. Edit `frontend/index.html` for structure
2. Edit `frontend/styles.css` for styling
3. Edit `frontend/script.js` for functionality
4. Follow existing patterns for API calls

### Environment Configuration
- Backend port: Change in `main.go` and Docker files
- Frontend API URL: Change `API_BASE_URL` in `script.js`
- Frontend port: Change in `serve.py` or use command line argument

## Production Deployment

### Backend
- Use multi-stage Docker build (already configured)
- Add environment variables for configuration
- Use external database instead of in-memory storage
- Add authentication and authorization
- Configure reverse proxy (nginx)

### Frontend
- Build and minify assets
- Use CDN for static files
- Configure proper CORS headers
- Add error boundaries and loading states
- Implement proper SEO if needed

## Troubleshooting

### Common Issues

1. **CORS Errors**: Use the provided Python server, don't open HTML directly
2. **API Connection Failed**: Ensure backend is running on port 8080
3. **Docker Permission Denied**: Add user to docker group or use sudo
4. **Port Already in Use**: Change ports in configuration files

### Logs and Debugging

```bash
# Backend logs
docker logs simple-rest-api

# Frontend logs
Check browser developer console

# API testing
curl -v http://localhost:8080/health
```

## Next Steps

Consider adding:
- Database integration (PostgreSQL, MongoDB)
- User authentication and authorization
- Input validation and sanitization
- Unit and integration tests
- CI/CD pipeline
- Monitoring and logging
- Rate limiting and security headers
- WebSocket support for real-time updates
