version: '3.8'

services:
  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: simple-rest-api-backend
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

  # Admin Frontend Service (HTML/CSS/JS)
  admin-frontend:
    build:
      context: ./admin-frontend
      dockerfile: Dockerfile
    container_name: simple-rest-api-admin-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    networks:
      - app-network

  # User Frontend Service (Streamlit)
  user-frontend:
    build:
      context: ./user-frontend
      dockerfile: Dockerfile
    container_name: simple-rest-api-user-frontend
    ports:
      - "8501:8501"
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
