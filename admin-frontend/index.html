<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple REST API Frontend</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 Simple REST API Frontend</h1>
            <p>Manage your items with this simple interface</p>
        </header>

        <!-- API Status -->
        <div class="status-section">
            <h2>API Status</h2>
            <button id="checkHealth" class="btn btn-info">Check Health</button>
            <div id="healthStatus" class="status-display"></div>
        </div>

        <!-- Create Item Form -->
        <div class="form-section">
            <h2>Create New Item</h2>
            <form id="createItemForm">
                <div class="form-group">
                    <label for="itemName">Name:</label>
                    <input type="text" id="itemName" name="name" required>
                </div>
                <div class="form-group">
                    <label for="itemDescription">Description:</label>
                    <textarea id="itemDescription" name="description" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Create Item</button>
            </form>
        </div>

        <!-- Items List -->
        <div class="items-section">
            <h2>Items List</h2>
            <div class="items-controls">
                <button id="refreshItems" class="btn btn-secondary">Refresh Items</button>
                <span id="itemsCount" class="items-count">0 items</span>
            </div>
            <div id="itemsList" class="items-container">
                <!-- Items will be loaded here -->
            </div>
        </div>

        <!-- Update Item Modal -->
        <div id="updateModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Update Item</h3>
                    <span class="close">&times;</span>
                </div>
                <form id="updateItemForm">
                    <input type="hidden" id="updateItemId">
                    <div class="form-group">
                        <label for="updateItemName">Name:</label>
                        <input type="text" id="updateItemName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="updateItemDescription">Description:</label>
                        <textarea id="updateItemDescription" name="description" required></textarea>
                    </div>
                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">Update Item</button>
                        <button type="button" class="btn btn-secondary" id="cancelUpdate">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notifications -->
        <div id="notifications" class="notifications"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
