# Simple REST API Frontend

A clean, responsive frontend interface for the Go REST API backend built with vanilla JavaScript, HTML, and CSS.

## Features

- **Health Check** - Monitor API status
- **View All Items** - Display all items in a clean grid layout
- **Create Items** - Add new items with name and description
- **Edit Items** - Update existing items with modal interface
- **Delete Items** - Remove items with confirmation
- **Real-time Notifications** - Success/error feedback
- **Responsive Design** - Works on desktop and mobile
- **Modern UI** - Clean, professional interface

## File Structure

```
frontend/
├── index.html      # Main HTML structure
├── styles.css      # CSS styling and responsive design
├── script.js       # JavaScript functionality and API calls
└── README.md       # This file
```

## Getting Started

### Prerequisites
- A web server (can be simple HTTP server)
- The Go REST API backend running on `http://localhost:8080`

### Option 1: Simple HTTP Server (Python)
```bash
cd frontend
python3 -m http.server 3000
```
Then open: `http://localhost:3000`

### Option 2: Simple HTTP Server (Node.js)
```bash
cd frontend
npx http-server -p 3000
```
Then open: `http://localhost:3000`

### Option 3: Live Server (VS Code Extension)
1. Install "Live Server" extension in VS Code
2. Right-click on `index.html`
3. Select "Open with Live Server"

### Option 4: Direct File Access
Simply open `index.html` in your browser (may have CORS issues with API calls)

## API Configuration

The frontend is configured to connect to the backend API at `http://localhost:8080`. 

To change the API URL, edit the `API_BASE_URL` constant in `script.js`:

```javascript
const API_BASE_URL = 'http://your-api-url:port';
```

## Features Overview

### 🏥 Health Check
- Click "Check Health" to verify API connectivity
- Shows green status for healthy API
- Shows red status for connection issues

### 📝 Create Items
- Fill in the "Name" and "Description" fields
- Click "Create Item" to add to the backend
- Form resets automatically on success

### 📋 View Items
- Items are displayed in a responsive grid
- Shows item ID, name, and description
- Click "Refresh Items" to reload from API

### ✏️ Edit Items
- Click "Edit" button on any item
- Modal popup with pre-filled form
- Update and save changes

### 🗑️ Delete Items
- Click "Delete" button on any item
- Confirmation dialog prevents accidental deletion
- Item removed from display on success

### 🔔 Notifications
- Success notifications (green) for completed actions
- Error notifications (red) for failed operations
- Info notifications (blue) for general information
- Auto-dismiss after 5 seconds

## Styling

The frontend uses a modern, professional design with:

- **Color Scheme**: Purple gradient primary colors
- **Typography**: Segoe UI font family
- **Layout**: CSS Grid and Flexbox for responsive design
- **Animations**: Smooth hover effects and transitions
- **Mobile-First**: Responsive design for all screen sizes

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## CORS Considerations

If you encounter CORS issues when running the frontend, you may need to:

1. **Update the Go backend** to include CORS headers
2. **Use a local web server** instead of opening HTML directly
3. **Run both frontend and backend on the same domain**

## Customization

### Changing Colors
Edit the CSS custom properties in `styles.css`:

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --error-color: #dc3545;
}
```

### Adding New Features
1. Add HTML elements in `index.html`
2. Style them in `styles.css`
3. Add JavaScript functionality in `script.js`
4. Follow the existing patterns for API calls and notifications

## Troubleshooting

### API Connection Issues
- Ensure the backend is running on `http://localhost:8080`
- Check browser console for CORS errors
- Verify API endpoints are accessible

### Frontend Not Loading
- Check if you're using a web server (not file:// protocol)
- Ensure all files are in the correct directory structure
- Check browser console for JavaScript errors

### Styling Issues
- Clear browser cache
- Check if CSS file is loading correctly
- Verify file paths are correct
