# Simple REST API in Go

A basic REST API backend built with Go and Gorilla Mux router.

## Features

- Simple CRUD operations for items
- In-memory storage (no database required)
- JSON responses
- Health check endpoint
- Clean REST API design

## Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check |
| GET | `/items` | Get all items |
| GET | `/items/{id}` | Get item by ID |
| POST | `/items` | Create new item |
| PUT | `/items/{id}` | Update item |
| DELETE | `/items/{id}` | Delete item |

## Getting Started

### Prerequisites
- Go 1.18 or higher (for local development)
- Docker (for containerized deployment)

### Option 1: Run with Docker (Recommended)

1. Build and run the container:
```bash
# Build the Docker image
docker build -t simple-rest-api .

# Run the container
docker run -d -p 8080:8080 --name simple-rest-api simple-rest-api
```

2. Or use the provided script:
```bash
# Make script executable
chmod +x run-docker.sh

# Start the container
./run-docker.sh start

# Test the API
./run-docker.sh test

# Stop the container
./run-docker.sh stop
```

### Option 2: Run Locally

1. Install dependencies:
```bash
go mod tidy
```

2. Run the server:
```bash
go run main.go
```

The server will start on `http://localhost:8080`

## API Usage Examples

### Health Check
```bash
curl http://localhost:8080/health
```

### Get All Items
```bash
curl http://localhost:8080/items
```

### Get Item by ID
```bash
curl http://localhost:8080/items/1
```

### Create New Item
```bash
curl -X POST http://localhost:8080/items \
  -H "Content-Type: application/json" \
  -d '{"name": "New Item", "description": "A new item description"}'
```

### Update Item
```bash
curl -X PUT http://localhost:8080/items/1 \
  -H "Content-Type: application/json" \
  -d '{"name": "Updated Item", "description": "Updated description"}'
```

### Delete Item
```bash
curl -X DELETE http://localhost:8080/items/1
```

## Item Structure

```json
{
  "id": 1,
  "name": "Item Name",
  "description": "Item Description"
}
```

## Docker Management

The included `run-docker.sh` script provides easy container management:

```bash
./run-docker.sh start    # Start the container
./run-docker.sh stop     # Stop and remove container
./run-docker.sh restart  # Restart the container
./run-docker.sh status   # Show container status
./run-docker.sh logs     # View container logs
./run-docker.sh build    # Build Docker image
./run-docker.sh test     # Test all API endpoints
```

## Docker Compose (Alternative)

You can also use docker-compose:

```bash
# Install docker-compose if not available
apt install docker-compose

# Start the service
docker-compose up -d

# Stop the service
docker-compose down
```

## Notes

- This uses in-memory storage, so data will be lost when the container/server restarts
- For production use, consider adding a database (PostgreSQL, MySQL, etc.)
- Error handling is basic but functional
- CORS is not configured (add if needed for frontend integration)
- The Docker container runs as a non-root user for security
- Health checks are configured for container monitoring
