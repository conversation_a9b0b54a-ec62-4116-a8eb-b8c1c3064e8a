// API Configuration
const API_BASE_URL = 'http://172.20.10.3:8080';

// DOM Elements
const elements = {
    checkHealthBtn: document.getElementById('checkHealth'),
    healthStatus: document.getElementById('healthStatus'),
    createItemForm: document.getElementById('createItemForm'),
    refreshItemsBtn: document.getElementById('refreshItems'),
    itemsList: document.getElementById('itemsList'),
    itemsCount: document.getElementById('itemsCount'),
    updateModal: document.getElementById('updateModal'),
    updateItemForm: document.getElementById('updateItemForm'),
    updateItemId: document.getElementById('updateItemId'),
    updateItemName: document.getElementById('updateItemName'),
    updateItemDescription: document.getElementById('updateItemDescription'),
    cancelUpdateBtn: document.getElementById('cancelUpdate'),
    closeModalBtn: document.querySelector('.close'),
    notifications: document.getElementById('notifications')
};

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    elements.notifications.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleString();
}

// API Functions
async function apiRequest(endpoint, options = {}) {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // Handle empty responses (like DELETE)
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }
        
        return null;
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// Health Check
async function checkHealth() {
    try {
        elements.healthStatus.innerHTML = '<div class="loading">Checking...</div>';
        
        const health = await apiRequest('/health');
        
        elements.healthStatus.innerHTML = `
            <div class="status-healthy">
                ✅ ${health.status.toUpperCase()}: ${health.message}
            </div>
        `;
        
        showNotification('API is healthy!', 'success');
    } catch (error) {
        elements.healthStatus.innerHTML = `
            <div class="status-error">
                ❌ ERROR: Unable to connect to API
            </div>
        `;
        
        showNotification('Failed to connect to API', 'error');
    }
}

// Load Items
async function loadItems() {
    try {
        const items = await apiRequest('/items');
        displayItems(items || []);
        elements.itemsCount.textContent = `${items ? items.length : 0} items`;
        
        if (items && items.length > 0) {
            showNotification(`Loaded ${items.length} items`, 'success');
        }
    } catch (error) {
        elements.itemsList.innerHTML = '<div class="error">Failed to load items</div>';
        elements.itemsCount.textContent = '0 items';
        showNotification('Failed to load items', 'error');
    }
}

// Display Items
function displayItems(items) {
    if (!items || items.length === 0) {
        elements.itemsList.innerHTML = `
            <div class="empty-state">
                <p>No items found. Create your first item!</p>
            </div>
        `;
        return;
    }
    
    elements.itemsList.innerHTML = items.map(item => `
        <div class="item-card" data-id="${item.id}">
            <div class="item-header">
                <div class="item-id">ID: ${item.id}</div>
            </div>
            <div class="item-name">${escapeHtml(item.name)}</div>
            <div class="item-description">${escapeHtml(item.description)}</div>
            <div class="item-actions">
                <button class="btn btn-warning" onclick="editItem(${item.id})">Edit</button>
                <button class="btn btn-danger" onclick="deleteItem(${item.id})">Delete</button>
            </div>
        </div>
    `).join('');
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Create Item
async function createItem(formData) {
    try {
        const newItem = await apiRequest('/items', {
            method: 'POST',
            body: JSON.stringify({
                name: formData.get('name'),
                description: formData.get('description')
            })
        });
        
        showNotification(`Item "${newItem.name}" created successfully!`, 'success');
        elements.createItemForm.reset();
        await loadItems();
    } catch (error) {
        showNotification('Failed to create item', 'error');
    }
}

// Edit Item
async function editItem(id) {
    try {
        const item = await apiRequest(`/items/${id}`);
        
        elements.updateItemId.value = item.id;
        elements.updateItemName.value = item.name;
        elements.updateItemDescription.value = item.description;
        
        elements.updateModal.style.display = 'block';
    } catch (error) {
        showNotification('Failed to load item for editing', 'error');
    }
}

// Update Item
async function updateItem(formData) {
    try {
        const id = elements.updateItemId.value;
        const updatedItem = await apiRequest(`/items/${id}`, {
            method: 'PUT',
            body: JSON.stringify({
                name: formData.get('name'),
                description: formData.get('description')
            })
        });
        
        showNotification(`Item "${updatedItem.name}" updated successfully!`, 'success');
        closeModal();
        await loadItems();
    } catch (error) {
        showNotification('Failed to update item', 'error');
    }
}

// Delete Item
async function deleteItem(id) {
    if (!confirm('Are you sure you want to delete this item?')) {
        return;
    }
    
    try {
        await apiRequest(`/items/${id}`, {
            method: 'DELETE'
        });
        
        showNotification('Item deleted successfully!', 'success');
        await loadItems();
    } catch (error) {
        showNotification('Failed to delete item', 'error');
    }
}

// Modal Functions
function closeModal() {
    elements.updateModal.style.display = 'none';
    elements.updateItemForm.reset();
}

// Event Listeners
elements.checkHealthBtn.addEventListener('click', checkHealth);
elements.refreshItemsBtn.addEventListener('click', loadItems);

elements.createItemForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    await createItem(formData);
});

elements.updateItemForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    await updateItem(formData);
});

elements.cancelUpdateBtn.addEventListener('click', closeModal);
elements.closeModalBtn.addEventListener('click', closeModal);

// Close modal when clicking outside
elements.updateModal.addEventListener('click', (e) => {
    if (e.target === elements.updateModal) {
        closeModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && elements.updateModal.style.display === 'block') {
        closeModal();
    }
});

// Initialize App
document.addEventListener('DOMContentLoaded', () => {
    showNotification('Frontend loaded successfully!', 'info');
    checkHealth();
    loadItems();
});

// Make functions globally available for onclick handlers
window.editItem = editItem;
window.deleteItem = deleteItem;
