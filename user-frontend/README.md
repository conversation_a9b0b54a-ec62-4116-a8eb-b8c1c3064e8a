# User Frontend - Streamlit Interface

A user-friendly Streamlit web application for managing items through the REST API backend.

## Features

- 🎨 **Modern UI**: Clean, intuitive interface built with Streamlit
- 📋 **View Items**: Browse all items with metrics and statistics
- ➕ **Add Items**: Easy form-based item creation
- ⚙️ **Manage Items**: Edit and delete existing items
- 🔍 **Health Check**: Monitor API connectivity
- 📊 **Dashboard**: Real-time metrics and insights

## Quick Start

### Option 1: Docker (Recommended)

```bash
# Build and run the container
docker build -t user-frontend .
docker run -d -p 8501:8501 --name user-frontend user-frontend
```

### Option 2: Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
streamlit run app.py
```

## Access

- **User Interface**: http://localhost:8501
- **Admin Interface**: http://localhost:3000 (HTML/CSS/JS)
- **Backend API**: http://localhost:8080

## Usage

1. **View Items**: See all items with statistics and quick actions
2. **Add New Item**: Use the form to create new items
3. **Manage Items**: Select items to edit or delete
4. **Health Check**: Verify API connectivity from the sidebar

## Configuration

The API base URL is configured in `app.py`:
```python
API_BASE_URL = "http://***********:8080"
```

Update this if your backend is running on a different address.

## Features Overview

### Dashboard View
- Total item count
- Average description length
- Latest item ID
- Quick delete actions

### Add Items
- Required name field
- Optional description
- Form validation
- Success feedback

### Manage Items
- Item selection dropdown
- Edit existing items
- Delete functionality
- Real-time updates

## Development

### File Structure
```
user-frontend/
├── app.py              # Main Streamlit application
├── requirements.txt    # Python dependencies
├── Dockerfile         # Container configuration
└── README.md          # This file
```

### Customization

- **Styling**: Modify the CSS in the `st.markdown()` sections
- **API URL**: Update `API_BASE_URL` constant
- **Features**: Add new pages by creating functions and updating the navigation

## Troubleshooting

1. **API Connection Issues**: Check if backend is running on the configured URL
2. **Port Conflicts**: Change the port in Docker run command if 8501 is busy
3. **Dependencies**: Ensure all requirements are installed correctly
