import streamlit as st
import requests
import json
from datetime import datetime
import pandas as pd

# Configuration
API_BASE_URL = "http://172.20.10.3:8080"

# Page configuration
st.set_page_config(
    page_title="Item Manager",
    page_icon="📦",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 0.75rem;
        border-radius: 0.25rem;
        border: 1px solid #c3e6cb;
        margin: 1rem 0;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 0.25rem;
        border: 1px solid #f5c6cb;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Helper functions
def make_api_request(endpoint, method="GET", data=None):
    """Make API request with error handling"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)
        elif method == "PUT":
            response = requests.put(url, json=data)
        elif method == "DELETE":
            response = requests.delete(url)
        
        response.raise_for_status()
        return response.json() if response.content else {}
    
    except requests.exceptions.ConnectionError:
        st.error("❌ Cannot connect to the API. Please check if the backend is running.")
        return None
    except requests.exceptions.HTTPError as e:
        st.error(f"❌ API Error: {e}")
        return None
    except Exception as e:
        st.error(f"❌ Unexpected error: {e}")
        return None

def check_api_health():
    """Check if API is healthy"""
    result = make_api_request("/health")
    return result is not None and result.get("status") == "healthy"

def get_all_items():
    """Get all items from API"""
    return make_api_request("/items")

def create_item(name, description):
    """Create a new item"""
    data = {"name": name, "description": description}
    return make_api_request("/items", method="POST", data=data)

def update_item(item_id, name, description):
    """Update an existing item"""
    data = {"name": name, "description": description}
    return make_api_request(f"/items/{item_id}", method="PUT", data=data)

def delete_item(item_id):
    """Delete an item"""
    return make_api_request(f"/items/{item_id}", method="DELETE")

# Main application
def main():
    # Header
    st.markdown('<h1 class="main-header">📦 Item Manager</h1>', unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.header("🔧 Actions")
        
        # API Health Check
        if st.button("🔍 Check API Health", use_container_width=True):
            if check_api_health():
                st.success("✅ API is healthy!")
            else:
                st.error("❌ API is not responding")
        
        st.divider()
        
        # Navigation
        page = st.selectbox(
            "📋 Choose Action",
            ["View Items", "Add New Item", "Manage Items"]
        )
    
    # Main content area
    if page == "View Items":
        show_items_page()
    elif page == "Add New Item":
        show_add_item_page()
    elif page == "Manage Items":
        show_manage_items_page()

def show_items_page():
    """Display all items in a user-friendly way"""
    st.header("📋 All Items")
    
    # Get items
    items = get_all_items()
    
    if items is None:
        return
    
    if not items:
        st.info("📭 No items found. Add some items to get started!")
        return
    
    # Display metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Items", len(items))
    with col2:
        avg_desc_length = sum(len(item.get('description', '')) for item in items) / len(items)
        st.metric("Avg Description Length", f"{avg_desc_length:.1f} chars")
    with col3:
        st.metric("Latest Item ID", max(item.get('id', 0) for item in items))
    
    st.divider()
    
    # Display items in cards
    for item in items:
        with st.container():
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.subheader(f"🏷️ {item.get('name', 'Unnamed Item')}")
                st.write(f"**Description:** {item.get('description', 'No description')}")
                st.caption(f"ID: {item.get('id', 'Unknown')}")
            
            with col2:
                st.write("")  # Spacing
                if st.button(f"🗑️ Delete", key=f"delete_{item.get('id')}", type="secondary"):
                    if delete_item(item.get('id')):
                        st.success("✅ Item deleted successfully!")
                        st.rerun()
                    else:
                        st.error("❌ Failed to delete item")
        
        st.divider()

def show_add_item_page():
    """Page for adding new items"""
    st.header("➕ Add New Item")
    
    with st.form("add_item_form"):
        st.subheader("📝 Item Details")
        
        name = st.text_input(
            "Item Name *",
            placeholder="Enter item name...",
            help="Give your item a descriptive name"
        )
        
        description = st.text_area(
            "Description",
            placeholder="Enter item description...",
            help="Provide additional details about the item",
            height=100
        )
        
        submitted = st.form_submit_button("🚀 Create Item", use_container_width=True)
        
        if submitted:
            if not name.strip():
                st.error("❌ Item name is required!")
            else:
                result = create_item(name.strip(), description.strip())
                if result:
                    st.success(f"✅ Item '{name}' created successfully!")
                    st.balloons()
                    # Clear form by rerunning
                    st.rerun()

def show_manage_items_page():
    """Page for managing (editing) items"""
    st.header("⚙️ Manage Items")
    
    # Get items
    items = get_all_items()
    
    if items is None:
        return
    
    if not items:
        st.info("📭 No items to manage. Add some items first!")
        return
    
    # Create a selectbox for choosing item to edit
    item_options = {f"{item['name']} (ID: {item['id']})": item for item in items}
    selected_item_key = st.selectbox(
        "🔍 Select Item to Edit",
        options=list(item_options.keys()),
        help="Choose an item to edit its details"
    )
    
    if selected_item_key:
        selected_item = item_options[selected_item_key]
        
        st.divider()
        
        with st.form("edit_item_form"):
            st.subheader(f"✏️ Edit Item (ID: {selected_item['id']})")
            
            new_name = st.text_input(
                "Item Name *",
                value=selected_item.get('name', ''),
                help="Update the item name"
            )
            
            new_description = st.text_area(
                "Description",
                value=selected_item.get('description', ''),
                help="Update the item description",
                height=100
            )
            
            col1, col2 = st.columns(2)
            
            with col1:
                update_submitted = st.form_submit_button("💾 Update Item", use_container_width=True)
            
            with col2:
                delete_submitted = st.form_submit_button("🗑️ Delete Item", use_container_width=True, type="secondary")
            
            if update_submitted:
                if not new_name.strip():
                    st.error("❌ Item name is required!")
                else:
                    result = update_item(selected_item['id'], new_name.strip(), new_description.strip())
                    if result:
                        st.success(f"✅ Item updated successfully!")
                        st.rerun()
            
            if delete_submitted:
                result = delete_item(selected_item['id'])
                if result:
                    st.success(f"✅ Item deleted successfully!")
                    st.rerun()

if __name__ == "__main__":
    main()
