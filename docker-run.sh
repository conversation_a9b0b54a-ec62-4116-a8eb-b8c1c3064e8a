#!/bin/bash

# Full-Stack Docker Management Script
# Manages both frontend and backend containers

set -e

BACKEND_IMAGE="simple-rest-api-backend"
FRONTEND_IMAGE="simple-rest-api-frontend"
BACKEND_CONTAINER="simple-rest-api-backend"
FRONTEND_CONTAINER="simple-rest-api-frontend"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

case "$1" in
    build)
        print_status "Building Docker images..."
        docker-compose build
        if [ $? -eq 0 ]; then
            print_success "Images built successfully!"
        else
            print_error "Failed to build images"
            exit 1
        fi
        ;;
    
    start|up)
        print_status "Starting full-stack application..."
        docker-compose up -d
        if [ $? -eq 0 ]; then
            print_success "Application started successfully!"
            echo ""
            echo "🌐 Frontend: http://localhost:3000"
            echo "🔧 Backend API: http://localhost:8080"
            echo "🏥 Health Check: http://localhost:8080/health"
            echo ""
            print_status "Waiting for services to be ready..."
            sleep 5
            ./docker-run.sh status
        else
            print_error "Failed to start application"
            exit 1
        fi
        ;;
    
    stop|down)
        print_status "Stopping application..."
        docker-compose down
        print_success "Application stopped"
        ;;
    
    restart)
        print_status "Restarting application..."
        docker-compose down
        docker-compose up -d
        if [ $? -eq 0 ]; then
            print_success "Application restarted successfully!"
            echo ""
            echo "🌐 Frontend: http://localhost:3000"
            echo "🔧 Backend API: http://localhost:8080"
        else
            print_error "Failed to restart application"
            exit 1
        fi
        ;;
    
    status)
        print_status "Checking application status..."
        echo ""
        docker-compose ps
        echo ""
        
        # Check backend health
        print_status "Testing backend health..."
        if curl -s http://localhost:8080/health > /dev/null 2>&1; then
            print_success "Backend is healthy"
        else
            print_warning "Backend is not responding"
        fi
        
        # Check frontend
        print_status "Testing frontend..."
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            print_success "Frontend is accessible"
        else
            print_warning "Frontend is not responding"
        fi
        ;;
    
    logs)
        if [ -z "$2" ]; then
            print_status "Showing logs for all services..."
            docker-compose logs -f
        else
            print_status "Showing logs for $2..."
            docker-compose logs -f "$2"
        fi
        ;;
    
    test)
        print_status "Testing API endpoints..."
        echo ""
        
        echo "1. Health check:"
        curl -s http://localhost:8080/health | jq . 2>/dev/null || curl -s http://localhost:8080/health
        echo -e "\n"
        
        echo "2. Get all items:"
        curl -s http://localhost:8080/items | jq . 2>/dev/null || curl -s http://localhost:8080/items
        echo -e "\n"
        
        echo "3. Create test item:"
        curl -s -X POST http://localhost:8080/items \
            -H "Content-Type: application/json" \
            -d '{"name": "Docker Test Item", "description": "Created from Docker container"}' | \
            jq . 2>/dev/null || curl -s -X POST http://localhost:8080/items \
            -H "Content-Type: application/json" \
            -d '{"name": "Docker Test Item", "description": "Created from Docker container"}'
        echo ""
        ;;
    
    clean)
        print_warning "This will remove all containers and images. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            print_status "Cleaning up..."
            docker-compose down --rmi all --volumes --remove-orphans
            print_success "Cleanup completed"
        else
            print_status "Cleanup cancelled"
        fi
        ;;
    
    dev)
        print_status "Starting in development mode with logs..."
        docker-compose up --build
        ;;
    
    *)
        echo "Full-Stack REST API Docker Management"
        echo ""
        echo "Usage: $0 {build|start|stop|restart|status|logs|test|clean|dev}"
        echo ""
        echo "Commands:"
        echo "  build    - Build Docker images"
        echo "  start    - Start the full-stack application"
        echo "  stop     - Stop the application"
        echo "  restart  - Restart the application"
        echo "  status   - Show application status and health"
        echo "  logs     - Show logs (optional: specify 'backend' or 'frontend')"
        echo "  test     - Test API endpoints"
        echo "  clean    - Remove all containers and images"
        echo "  dev      - Start in development mode with logs"
        echo ""
        echo "Examples:"
        echo "  $0 start              # Start the application"
        echo "  $0 logs backend       # Show backend logs"
        echo "  $0 logs frontend      # Show frontend logs"
        echo "  $0 test               # Test all API endpoints"
        exit 1
        ;;
esac
